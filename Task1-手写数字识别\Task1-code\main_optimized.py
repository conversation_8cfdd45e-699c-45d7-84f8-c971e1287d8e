import mnist_loader
import network_optimized as network
import os
import time
import numpy as np

def set_numpy_threads():
    """设置NumPy使用多线程"""
    # 设置NumPy使用所有可用的CPU核心
    os.environ['OMP_NUM_THREADS'] = str(os.cpu_count())
    os.environ['MKL_NUM_THREADS'] = str(os.cpu_count())
    os.environ['NUMEXPR_NUM_THREADS'] = str(os.cpu_count())

    # 如果有Intel MKL，启用多线程
    try:
        import mkl
        mkl.set_num_threads(os.cpu_count())
        print(f"Intel MKL 多线程已启用，使用 {os.cpu_count()} 个线程")
    except ImportError:
        pass

    print(f"NumPy 配置为使用 {os.cpu_count()} 个CPU核心")

if __name__ == '__main__':
    # 改变工作路径至当前文件目录
    os.chdir(os.path.abspath(os.path.dirname(__file__)))

    # 优化NumPy性能
    set_numpy_threads()

    # 学习率衰减实验参数
    epochs = 20
    mini_batch_size = 10
    eta = 0.5  # 初始学习率
    net_sizes = [784, 192, 30, 10]
    lambda_val = 0.0

    output_pic = 'lr_decay_epochs' + str(epochs) + '_result.jpg'

    print("=== 学习率衰减实验 ===")
    print(f"网络结构: {net_sizes}")
    print(f"训练轮数: {epochs}")
    print(f"批次大小: {mini_batch_size}")
    print(f"初始学习率: {eta}")
    print(f"学习率衰减: 每5个epoch衰减至0.8倍")
    print(f"正则化参数: {lambda_val}")
    print()

    # 加载数据
    print("正在加载MNIST数据集...")
    start_time = time.time()
    training_data, validation_data, test_data = mnist_loader.load_data_wrapper()
    load_time = time.time() - start_time
    print(f"数据加载完成，用时: {load_time:.2f}秒")
    print()

    # 创建优化的网络
    net = network.OptimizedNetwork(net_sizes, cost=network.CrossEntropyCost)

    # 开始训练
    total_start_time = time.time()
    test_cost, test_accuracy, training_cost, training_accuracy \
        = net.SGD(training_data, epochs, mini_batch_size, eta, lmbda=lambda_val, evaluation_data=test_data,
                  monitor_evaluation_cost=True, monitor_evaluation_accuracy=True,
                  monitor_training_cost=True, monitor_training_accuracy=True,
                  lr_decay=True, decay_rate=0.8, decay_epochs=5)

    total_time = time.time() - total_start_time

    print("=== 训练完成 ===")
    print(f"总训练时间: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
    print(f"平均每个epoch用时: {total_time/epochs:.2f}秒")

    if test_accuracy:
        print(f"最终测试准确率: {test_accuracy[-1]:.4f}")
    if training_accuracy:
        print(f"最终训练准确率: {training_accuracy[-1]:.4f}")

    # 绘制结果
    print("正在生成训练结果图表...")
    network.plot_result(epochs, test_cost, test_accuracy, training_cost, training_accuracy, output_pic)
    print(f"结果图表已保存为: output/{output_pic}")

    # 性能统计
    print("\n=== 性能统计 ===")
    training_samples = 50000  # MNIST训练集大小
    total_samples_processed = training_samples * epochs
    samples_per_second = total_samples_processed / total_time
    print(f"处理样本总数: {total_samples_processed:,}")
    print(f"处理速度: {samples_per_second:.0f} 样本/秒")
