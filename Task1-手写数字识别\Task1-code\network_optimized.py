# 标准库
import random
import time

# 第三方库
import numpy as np
import matplotlib.pyplot as plt


# 定义 cross-entropy 和 quadratic 损失函数
class CrossEntropyCost(object):

    @staticmethod
    def fn(a, y):
        # 批处理版本的交叉熵损失
        return -np.sum(y * np.log(a + 1e-10)) / a.shape[1]

    @staticmethod
    def delta(z, a, y):
        """返回输出层的误差方程 δ^L """
        return a - y


class QuadraticCost(object):

    @staticmethod
    def fn(a, y):
        """返回 a 和标签 y 之间的损失"""
        return 0.5 * np.mean(np.sum((a - y) ** 2, axis=0))

    @staticmethod
    def delta(z, a, y):
        """返回输出层的误差方程 δ^L """
        return (a - y) * sigmoid_prime(z)


# 优化的激活函数
def sigmoid(z):
    """优化的sigmoid激活函数，防止溢出"""
    # 使用稳定的sigmoid实现
    z = np.clip(z, -500, 500)  # 防止溢出
    return 1.0 / (1.0 + np.exp(-z))


def sigmoid_prime(z):
    """sigmoid激活函数的导数"""
    s = sigmoid(z)
    return s * (1 - s)


def softmax(z):
    """批处理版本的softmax函数"""
    # 防止溢出的稳定softmax
    z_shifted = z - np.max(z, axis=0, keepdims=True)
    exp_z = np.exp(z_shifted)
    return exp_z / np.sum(exp_z, axis=0, keepdims=True)


# 定义优化的主网络
class OptimizedNetwork(object):

    def __init__(self, sizes, cost=CrossEntropyCost):
        """
        优化版本的神经网络，支持批处理训练
        """
        self.num_layers = len(sizes)
        self.sizes = sizes
        self.default_weight_initializer()
        self.cost = cost

    def default_weight_initializer(self):
        """使用Xavier初始化"""
        self.biases = [np.random.randn(y, 1) for y in self.sizes[1:]]
        self.weights = [np.random.randn(y, x) / np.sqrt(x)
                        for x, y in zip(self.sizes[:-1], self.sizes[1:])]

    def feedforward(self, a):
        """批处理前向传播"""
        for b, w in zip(self.biases[:-1], self.weights[:-1]):
            a = sigmoid(np.dot(w, a) + b)

        # 输出层使用softmax
        b, w = self.biases[-1], self.weights[-1]
        a = softmax(np.dot(w, a) + b)
        return a

    def SGD(self, training_data, epochs, mini_batch_size, eta,
            lmbda=0.0,
            evaluation_data=None,
            monitor_evaluation_cost=False,
            monitor_evaluation_accuracy=False,
            monitor_training_cost=False,
            monitor_training_accuracy=False,
            lr_decay=False,
            decay_rate=0.8,
            decay_epochs=5):
        """
        优化的SGD算法，使用批处理
        """
        training_data = list(training_data)
        n = len(training_data)

        if evaluation_data:
            evaluation_data = list(evaluation_data)
            n_data = len(evaluation_data)

        evaluation_cost, evaluation_accuracy = [], []
        training_cost, training_accuracy = [], []

        print(f"开始训练，共{epochs}个epoch，每个mini-batch大小：{mini_batch_size}")
        if lr_decay:
            print(f"学习率衰减：每{decay_epochs}个epoch衰减率为{decay_rate}")

        current_eta = eta  # 当前学习率

        for j in range(epochs):
            # 学习率衰减
            if lr_decay and j > 0 and j % decay_epochs == 0:
                current_eta *= decay_rate
                print(f"学习率衰减：从 {current_eta/decay_rate:.4f} 降至 {current_eta:.4f}")

            random.shuffle(training_data)
            mini_batches = [training_data[k:k + mini_batch_size]
                          for k in range(0, n, mini_batch_size)]

            for mini_batch in mini_batches:
                self.update_mini_batch_vectorized(mini_batch, current_eta, lmbda, len(training_data))

            print("Epoch %s training complete" % j)

            if monitor_training_cost:
                cost = self.total_cost(training_data, lmbda)
                training_cost.append(cost)
                print("Cost on training data: {}".format(cost))
            if monitor_training_accuracy:
                accuracy = self.accuracy(training_data, convert=True) / n
                training_accuracy.append(accuracy)
                print("Accuracy on training data: {} / {}".format(accuracy * n, n))
            if monitor_evaluation_cost:
                cost = self.total_cost(evaluation_data, lmbda, convert=True)
                evaluation_cost.append(cost)
                print("Cost on evaluation data: {}".format(cost))
            if monitor_evaluation_accuracy:
                accuracy = self.accuracy(evaluation_data) / n_data
                evaluation_accuracy.append(accuracy)
                print("Accuracy on evaluation data: {} / {}\n".format(accuracy * n_data, n_data))

        return evaluation_cost, evaluation_accuracy, training_cost, training_accuracy

    def update_mini_batch_vectorized(self, mini_batch, eta, lmbda, n):
        """
        向量化的mini-batch更新，大幅提升性能
        """
        # 将mini-batch转换为矩阵形式
        X = np.column_stack([x.ravel() for x, y in mini_batch])
        Y = np.column_stack([y.ravel() for x, y in mini_batch])

        # 批处理反向传播
        nabla_b, nabla_w = self.backprop_vectorized(X, Y)

        # 更新权重和偏置
        self.weights = [(1 - eta * (lmbda / n)) * w - (eta / len(mini_batch)) * nw
                       for w, nw in zip(self.weights, nabla_w)]
        self.biases = [b - (eta / len(mini_batch)) * nb
                      for b, nb in zip(self.biases, nabla_b)]

    def backprop_vectorized(self, X, Y):
        """
        向量化的反向传播算法，处理整个批次
        """
        nabla_b = [np.zeros(b.shape) for b in self.biases]
        nabla_w = [np.zeros(w.shape) for w in self.weights]

        # 前向传播
        activation = X
        activations = [X]
        zs = []

        for b, w in zip(self.biases[:-1], self.weights[:-1]):
            z = np.dot(w, activation) + b
            zs.append(z)
            activation = sigmoid(z)
            activations.append(activation)

        # 输出层
        z = np.dot(self.weights[-1], activation) + self.biases[-1]
        zs.append(z)
        activation = softmax(z)
        activations.append(activation)

        # 反向传播
        delta = self.cost.delta(zs[-1], activations[-1], Y)
        nabla_b[-1] = np.sum(delta, axis=1, keepdims=True)
        nabla_w[-1] = np.dot(delta, activations[-2].T)

        for l in range(2, self.num_layers):
            z = zs[-l]
            sp = sigmoid_prime(z)
            delta = np.dot(self.weights[-l+1].T, delta) * sp
            nabla_b[-l] = np.sum(delta, axis=1, keepdims=True)
            nabla_w[-l] = np.dot(delta, activations[-l-1].T)

        return nabla_b, nabla_w

    def accuracy(self, data, convert=False):
        """优化的批处理准确率计算"""
        # 将数据转换为批处理格式以提高速度
        batch_size = 1000  # 批处理大小
        correct = 0
        total = 0

        data_list = list(data)
        for i in range(0, len(data_list), batch_size):
            batch = data_list[i:i+batch_size]

            # 构建批处理矩阵
            X_batch = np.column_stack([x.ravel() for x, y in batch])

            # 批处理前向传播
            predictions = self.feedforward(X_batch)
            pred_labels = np.argmax(predictions, axis=0)

            if convert:
                true_labels = np.array([np.argmax(y) for x, y in batch])
            else:
                true_labels = np.array([y for x, y in batch])

            correct += np.sum(pred_labels == true_labels)
            total += len(batch)

        return correct

    def total_cost(self, data, lmbda, convert=False):
        """优化的批处理损失计算"""
        batch_size = 1000
        total_cost = 0.0
        data_list = list(data)
        n = len(data_list)

        for i in range(0, n, batch_size):
            batch = data_list[i:i+batch_size]

            # 构建批处理矩阵
            X_batch = np.column_stack([x.ravel() for x, y in batch])

            if convert:
                Y_batch = np.column_stack([vectorized_result(y).ravel() for x, y in batch])
            else:
                Y_batch = np.column_stack([y.ravel() for x, y in batch])

            # 批处理前向传播和损失计算
            a_batch = self.feedforward(X_batch)
            batch_cost = self.cost.fn(a_batch, Y_batch) * len(batch) / n
            total_cost += batch_cost

        # 添加L2正则化项
        regularization = 0.5 * (lmbda / n) * sum(np.linalg.norm(w) ** 2 for w in self.weights)
        return total_cost + regularization


def vectorized_result(j):
    """将对应的数字 (0...9) 转化为对应的 one-hot 向量"""
    e = np.zeros((10, 1))
    e[j] = 1.0
    return e


def plot_result(epochs, test_cost, test_accuracy, training_cost, training_accuracy, file_name):
    """绘制训练集和测试集的损失及准确率, 并将所得结果保存"""
    epoch = np.arange(epochs)
    plt.subplot(1, 2, 1)
    plt.plot(epoch, test_cost, 'r', label='test_cost')
    plt.plot(epoch, training_cost, 'k', label='training_cost')
    plt.title("Cost Range")
    plt.legend()
    plt.subplot(1, 2, 2)
    plt.plot(epoch, test_accuracy, 'r', label='test_accuracy')
    plt.plot(epoch, training_accuracy, 'k', label='training_accuracy')
    plt.title("Accuracy Range")
    plt.legend()
    plt.savefig('output/' + file_name)
