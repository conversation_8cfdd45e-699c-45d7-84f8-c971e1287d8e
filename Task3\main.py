import copy
import os.path
import string
import nltk
import numpy as np

# 设置NLTK数据路径
nltk.data.path.append('./nltk_data')

#  加载停用词
stopwords = set(nltk.corpus.stopwords.words('english'))
#  文本类别
text_type = {'World': 0, 'Sci/Tech': 1, 'Sports': 2, 'Business': 3}


def load(path, type_r, remove_stopwords=True):
    """
    加载数据并进行预处理
    :param path: 数据路径
    :param type_r: 使用的单词还原方法
    :param remove_stopwords: 是否去除停用词，默认为True
    """
    x, y = [], []
    file = open(path, 'r')
    trans = str.maketrans('', '', string.punctuation)
    if type_r == 'stemmer':
        re = nltk.stem.porter.PorterStemmer().stem
    elif type_r == 'lemmatizer':
        re = nltk.stem.WordNetLemmatizer().lemmatize
    else:
        raise ValueError('type error')
    for line in file:
        temp = line.split('|')  # 将类别和文本分离开
        # 预处理文本
        sent = temp[1].strip().lower()  # 全小写
        sent = sent.translate(trans)  # 去除标点符号
        sent = nltk.word_tokenize(sent)  # 将文本标记化

        # 根据参数决定是否去停用词
        if remove_stopwords:
            sent = [s for s in sent if not ((s in stopwords) or s.isdigit())]  # 去停用词和数字
        else:
            sent = [s for s in sent if not s.isdigit()]  # 只去数字，保留停用词

        sent = [re(s) for s in sent]  # 还原: 词干提取/词形还原
        x.append(sent)
        #  预处理类别
        y.append(text_type[temp[0].strip()])
    file.close()
    return x, y


def words2dic(sent):
    """
    生成数据对应的文本库id
    :param sent: 数据集
    :return: 文本库
    """
    dicts = {}
    i = 0
    for words in sent:
        for word in words:
            if word not in dicts:
                dicts[word] = i
                i += 1
    return dicts


def train_TF(data_x, data_y):
    """
    朴素贝叶斯训练
    :param data_x:训练数据
    :param data_y:真值
    """
    # 构建词典，用于生成统计矩阵
    dicts = words2dic(data_x)
    # n(w_i in w_c) 创建词频矩阵
    word_fre = np.zeros((len(dicts), 4), dtype=np.int32)
    # n(c, text) 每类下的句总数
    sent_fre = np.zeros((1, 4), dtype=np.int32)
    # 更新矩阵
    for x, y in zip(data_x, data_y):
        for word in x:
            word_fre[dicts[word], y] += 1
        sent_fre[0, y] += 1
    # TODO 计算P(c)
    # P(c) = N(c,text) / N(text)，即每个类别的文档数除以总文档数
    total_docs = np.sum(sent_fre)  # 总文档数
    p_c = sent_fre[0] / total_docs  # 每个类别的先验概率

    # TODO 计算P(w_i|c)，并加入拉普拉斯平滑
    # P(w_i|c) = (N(w_i in W_c) + 1) / (N(W_c) + m)
    # N(W_c)：类别c中所有词的总数
    word_count_per_class = np.sum(word_fre, axis=0)  # 每个类别的总词数
    vocab_size = len(dicts)  # 词典大小m

    # 拉普拉斯平滑：分子加1，分母加词典大小
    p_stage = (word_fre + 1) / (word_count_per_class + vocab_size)

    return dicts, p_stage, p_c


def test_TF(data_x, data_y, dicts, p_stage, p_c):
    """
    测试准确率
    """
    # 计算ln P(c)
    ln_p_c = np.log(p_c)
    # 计算ln P(w_i|c)
    ln_p_s = np.log(p_stage)
    # 计算准确率
    count = 0
    for x, y in zip(data_x, data_y):
        # TODO 计算对应的预测值并统计，注意过滤未收录词
        # c_text = argmax_c [ln P(c) + Σ ln P(w_i|c)]

        # 初始化每个类别的对数概率，从先验概率开始
        log_prob = ln_p_c.copy()  # ln P(c)

        # 对文档中的每个词，累加其对数条件概率
        for word in x:
            # 跳过不在训练词典中的词
            if word in dicts:
                word_idx = dicts[word]
                # 累加 ln P(w_i|c) 到每个类别
                log_prob += ln_p_s[word_idx, :]

        # 找到概率最大的类别
        p = log_prob
        if np.argmax(p) == y:
            count += 1
    print('Accuracy: {}/{} {:.2f}%'.format(count, len(data_y), 100*count/len(data_y)))


# def train_B(data_x, data_y):
#     # TODO 扩展，实现Bernoulli方法
#     # 构建词典，用于生成统计矩阵

#     # n(w in w_c) 创建词频矩阵

#     # n(c, text) 每类下的句总数

#     # 计算P(d_?|c)

#     return dicts, p_stage, p_c


# def test_B(data_x, data_y, dicts, p_stage, p_c):
#     # TODO 扩展，实现Bernoulli方法

#     print('Accuracy: {}/{} {:.2f}%'.format(count, len(data_y), 100 * count / len(data_y)))


if __name__ == '__main__':
    '''超参数设置'''
    # 单词还原方法
    type_re = ['stemmer', 'lemmatizer'][0]
    # 训练方法
    type_train = ['TF', 'Bernoulli'][0]

    print('训练方法: {}'.format(type_train))
    print('还原方法: {}'.format(type_re))

    '''读取训练数据并进行预处理'''
    train_x, train_y = load('./data/news_category_train_mini.csv', type_re, remove_stopwords = False)
    test_x, test_y = load('./data/news_category_test_mini.csv', type_re, remove_stopwords = False)
    print('load success')

    '''开始训练'''
    if type_train == 'TF':
        dictionary, p_stage, p_c = train_TF(train_x, train_y)
    # elif type_train == 'Bernoulli':
    #     dictionary, p_stage, p_c = train_B(train_x, train_y)

    '''计算准确率'''
    if type_train == 'TF':
        test_TF(train_x, train_y, dictionary, p_stage, p_c)
        test_TF(test_x, test_y, dictionary, p_stage, p_c)
    # elif type_train == 'Bernoulli':
    #     test_B(train_x, train_y, dictionary, p_stage, p_c)
    #     test_B(test_x, test_y, dictionary, p_stage, p_c)

