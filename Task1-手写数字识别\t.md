2. 实验目的
本次实验旨在通过实现手写数字识别任务，掌握深度学习的核心技能和理论知识。具体需要掌握的三项关键技能包括：

神经网络搭建：学习如何设计和构建多层前馈神经网络架构，包括输入层、隐藏层和输出层的设计，理解不同层之间的连接方式和参数初始化方法。
反向传播算法实现：深入理解并实现反向传播算法，掌握梯度计算的链式法则，学会如何通过误差反向传播来更新网络权重和偏置。
超参数调优：学习如何选择和调整关键超参数，包括学习率、批次大小、网络层数、神经元数量等，理解这些参数对模型性能的影响。
4. 实验背景与理论
4.1 数据集简介
本实验使用MNIST手写数字数据集，这是机器学习领域的经典基准数据集。MNIST数据集包含70,000张28×28像素的灰度手写数字图像，数字范围为0-9。

数据集划分：

训练集：50,000张图像，用于模型训练
验证集：10,000张图像，用于模型验证和超参数调优
测试集：10,000张图像，用于最终模型性能评估
每张图像被展平为784维的向量（28×28=784），像素值范围为[0,1]。标签采用one-hot编码形式，即10维向量，对应数字的位置为1，其余位置为0。

4.2 神经网络和反向传播
4.2.1 前向传播
神经网络的前向传播过程可以表示为：

对于第$l$层：
$$z^{(l)} = W^{(l)}a^{(l-1)} + b^{(l)}$$
$$a^{(l)} = f(z^{(l)})$$

其中：

$W^{(l)}$是第$l$层的权重矩阵
$b^{(l)}$是第$l$层的偏置向量
$a^{(l-1)}$是第$l-1$层的激活输出
$f(\cdot)$是激活函数
4.2.2 反向传播算法
反向传播算法基于链式法则计算梯度，核心方程包括：

BP1 - 输出层误差：
$$\delta^{(L)} = \nabla_a C \odot f'(z^{(L)})$$

对于交叉熵损失函数和softmax激活函数的组合：
$$\delta^{(L)} = a^{(L)} - y$$

BP2 - 隐藏层误差：
$$\delta^{(l)} = ((W^{(l+1)})^T \delta^{(l+1)}) \odot f'(z^{(l)})$$

BP3 - 偏置梯度：
$$\frac{\partial C}{\partial b^{(l)}} = \delta^{(l)}$$

BP4 - 权重梯度：
$$\frac{\partial C}{\partial W^{(l)}} = \delta^{(l)} (a^{(l-1)})^T$$

4.3 激活函数
4.3.1 Sigmoid函数
数学表达式：
$$\sigma(z) = \frac{1}{1 + e^{-z}}$$

导数：
$$\sigma'(z) = \sigma(z)(1 - \sigma(z))$$

Sigmoid函数将输入映射到(0,1)区间，具有平滑的S型曲线特征。

4.3.2 Softmax函数
数学表达式：
$$\text{softmax}(z_i) = \frac{e^{z_i}}{\sum_{j=1}^{K} e^{z_j}}$$

Softmax函数用于多分类问题的输出层，确保所有输出概率之和为1。

4.3.3 ReLU函数
数学表达式：
$$\text{ReLU}(x) = \max(0, x)$$

导数：
$$\text{ReLU}'(x) = \begin{cases}
1 & \text{if } x > 0 \\
0 & \text{if } x \leq 0
\end{cases}$$

4.4 损失函数
4.4.1 交叉熵损失函数
数学表达式：
$$C = -\sum_{i=1}^{n} y_i \log(a_i)$$

其中$y_i$是真实标签的one-hot编码，$a_i$是网络输出的预测概率。

梯度：
$$\frac{\partial C}{\partial a_i} = -\frac{y_i}{a_i}$$

交叉熵损失函数特别适用于分类问题，能够有效地惩罚错误分类。

4.4.2 二次损失函数
数学表达式：
$$C = \frac{1}{2}\|a - y\|^2$$

梯度：
$$\frac{\partial C}{\partial a} = a - y$$

4.5 优化算法
4.5.1 随机梯度下降（SGD）
参数更新公式：
$$W^{(l)} \leftarrow W^{(l)} - \eta \frac{\partial C}{\partial W^{(l)}}$$
$$b^{(l)} \leftarrow b^{(l)} - \eta \frac{\partial C}{\partial b^{(l)}}$$

其中$\eta$是学习率。

4.5.2 小批量梯度下降
对于小批量大小为$m$的情况：
$$\frac{\partial C}{\partial W^{(l)}} = \frac{1}{m} \sum_{i=1}^{m} \frac{\partial C_i}{\partial W^{(l)}}$$

4.5.3 L2正则化
为防止过拟合，添加L2正则化项：
$$C_{total} = C + \frac{\lambda}{2n} \sum_w w^2$$

权重更新公式变为：
$$W^{(l)} \leftarrow (1 - \eta\frac{\lambda}{n})W^{(l)} - \eta \frac{\partial C}{\partial W^{(l)}}$$

其中$\lambda$是正则化参数，$n$是训练样本总数。